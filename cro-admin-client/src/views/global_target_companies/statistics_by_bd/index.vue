<template>
  <div class="maincontent">
    <!-- 颜色图例说明 -->
    <div class="legend-container">
      <div class="legend-title">Business Status Legend:</div>
      <div class="legend-items">
        <div v-for="item in rowConfig" :key="item.description" class="legend-item">
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <span class="legend-text">{{ item.description }}</span>
        </div>
      </div>
    </div>

    <el-table :data="tableData" style="width: 100%" :cell-style="cellStyle" @cell-mouse-enter="handleCellMouseEnter" @cell-mouse-leave="handleCellMouseLeave">
      <!-- 左侧说明列 -->
      <el-table-column fixed="left" width="150px" label="">
        <template #default="scope">
          <div class="desc-cell" :style="{ fontWeight: 'bold', textAlign: 'center' }">
            <span> {{ businessStatusMap[scope.$index]?.description }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-for="region in regions" align="center" :label="region.name">
        <el-table-column v-for="user in region.user_data" :prop="user.username" :label="user.username" align="center">
          <template #default="scope">
            <span @click="handleCellClick(scope.$index, region.region_id, user.user_id)">
              {{ scope.row[region.name + "_" + user.username] }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { getCompanyStatisticsByBD } from "@/api/global_target_companies";
import { message } from "@/utils/message";

defineOptions({
  name: "StatisticsByBD"
});
const router = useRouter();

// 行配置：描述和颜色的对应关系
const businessStatusMap = [
  { business_status: 1, description: "Existing", color: "#52c41a" }, // 绿色
  { business_status: 2, description: "High Potential", color: "#faad14" }, // 黄色
  { business_status: 3, description: "Low Potential", color: "#ff4d4f" }, // 红色
  { business_status: 4, description: "Not Active", color: "#8c8c8c" } // 灰色
];

const tableData = ref([]);
const regions = ref([]);

const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
  // 第一列（描述列）不参与背景色渲染
  if (columnIndex === 0) {
    return {
      backgroundColor: "#fff", // 白色背景
      fontWeight: "bold"
    };
  }

  // 根据行索引获取对应的背景色
  const backgroundColor = businessStatusMap[rowIndex]?.color || "#f0f0f0"; // 默认浅灰色

  return {
    backgroundColor: backgroundColor,
    color: "#fff", // 白色文字确保可读性
    fontWeight: "bold"
  };
};

const handleCellMouseEnter = (row, column, cell, event) => {
  const value = cell.textContent.trim();
  if (value>0) {
    cell.style.cursor = "pointer";
  }
};

const handleCellMouseLeave = (row, column, cell, event) => {
  cell.style.cursor = "default";
};

const handleCellClick = (rowIndex, regionId, userId) => {
  const businessStatus = businessStatusMap[rowIndex].business_status; // 行数从 1 开始
  router.push({
    name: "Company",
    state: { responsibleUserId: userId, territoryIdFromBD: regionId, businessStatus: businessStatus }
  });
};

const processData = resData => {
  const mapLength = businessStatusMap.length;
  const processedData = Array.from({ length: mapLength }, () => ({}));
  resData.forEach(regionItem => {
    const { region, user_data } = regionItem;
    let users = [];
    user_data.forEach(userDataItem => {
      const { username, user_id, counts } = userDataItem;
      users.push({ username: username, user_id: user_id });
      businessStatusMap.forEach((businessStatusItem, index) => {
        processedData[index][region + "_" + username] = counts[businessStatusItem.business_status] || 0;
      });
    });
    regions.value.push({ name: region, region_id: regionItem.id, user_data: users });
  });
  console.log(processedData);
  return processedData;
};

const getStatisticsByBDData = async () => {
  const res = await getCompanyStatisticsByBD();
  if (res.success) {
    tableData.value = processData(res.data);
  } else {
    message("获取数据失败", { type: "error" });
  }
};
onMounted(() => {
  getStatisticsByBDData();
});
</script>

<style scoped lang="scss"></style>
