<template>
  <div class="maincontent">
    <div class="handlebar">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" :model="searchParams" class="search-bar">
            <el-form-item label="Company Name">
              <el-input v-model="searchParams.name" clearable />
            </el-form-item>
            <el-form-item label="Business Status" style="width: 300px">
              <el-select multiple collapse-tags placeholder="Select" v-model="searchParams.business_statuses" clearable>
                <el-option v-for="item in businessStatusOptions" :key="item.value" :label="item.text" :value="item.value" />
              </el-select>
            </el-form-item>
            <Auth value="permission:search_item">
              <el-form-item label="Responsible User" style="width: 300px">
                <el-select multiple collapse-tags placeholder="Select" v-model="searchParams.responsible_users" clearable>
                  <el-option v-for="user in bdUsers" :key="user.id" :label="user.username" :value="user.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="Territory">
                <el-cascader v-model="searchParams.territory_full_path" :options="territoryOptions" placeholder="Select" collapse-tags collapse-tags-tooltip clearable :show-all-levels="false" :props="cascaderProps" />
              </el-form-item>
            </Auth>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">Search</el-button>
              <el-button type="success" :icon="Download" @click="handleExport" :loading="exportLoading">Export Excel</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4">
          <el-button type="success" @click="handleAdd()">Add</el-button>
          <Auth value="permission:btn:transfer">
            <el-button type="warning" @click="handleTransfer()" :icon="Switch">Transfer</el-button>
          </Auth>
          <el-button type="primary">
            Download<el-icon class="el-icon--right" @click="exportExcelCompany({})"><Download /></el-icon>
          </el-button>
          <el-button type="primary" @click="batchDelete()" :icon="Delete" circle />
        </el-col>
      </el-row>
    </div>
    <el-container>
      <!-- 表格 -->
      <el-main>
        <el-table :data="companyList" :row-class-name="tableRowClassName" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column fixed prop="name" label="Name" />
          <el-table-column prop="territory.name" label="Territory Name" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="contact" label="Contact" />
          <el-table-column prop="business_status_display" label="Business Status" />
          <el-table-column prop="responsible_user.username" label="Responsible User" />
          <el-table-column prop="remark" label="Remark" />
          <el-table-column label="Operation">
            <template #default="scope">
              <el-button type="primary" @click="handleEdit(scope.row)" :icon="Edit" circle />
            </template>
          </el-table-column>
        </el-table>
      </el-main>

      <el-footer>
        <el-pagination style="margin-top: 20px" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[20, 50, 100]" :page-size="pagination.pageSize" layout="->,total, sizes, prev, pager, next, jumper" :total="pagination.total" />
      </el-footer>
    </el-container>
    <!-- 合并后的对话框 -->
    <el-dialog :title="isEditMode ? 'Edit Company' : 'Add Company'" v-model="dialogVisible">
      <el-form ref="formRef" :model="companyForm" label-width="150px" :rules="rules">
        <el-form-item label="Name" prop="name">
          <el-input v-model="companyForm.name" />
        </el-form-item>
        <el-form-item label="Address">
          <el-input v-model="companyForm.address" />
        </el-form-item>
        <el-form-item label="Contact">
          <el-input v-model="companyForm.contact" />
        </el-form-item>
        <el-form-item label="Business Status" prop="business_status">
          <el-select placeholder="Select" v-model="companyForm.business_status">
            <el-option v-for="item in businessStatusOptions" :key="item.value" :label="item.text" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Territory" prop="territory_full_path">
          <el-cascader
            v-model="companyForm.territory_full_path"
            :options="territoryOptions"
            :props="{
              expandTrigger: 'hover' as const
            }"
          />
        </el-form-item>
        <el-form-item label="Remark">
          <el-input v-model="companyForm.remark" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Transfer对话框 -->
    <el-dialog title="Transfer responsible user" v-model="transferDialogVisible" width="600px">
      <div style="margin-bottom: 20px">
        <h4>Selected companies ({{ selectedCompanies.length }}):</h4>
        <el-tag v-for="company in selectedCompanies" :key="company.id" style="margin: 2px">
          {{ company.name }}
        </el-tag>
      </div>

      <el-form>
        <el-form-item label="New responsible user">
          <el-select v-model="selectedUserId" placeholder="Please select a new responsible user" style="width: 100%">
            <el-option v-for="user in bdUsers" :key="user.id" :label="user.username" :value="user.id" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="transferDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleTransferConfirm" :loading="transferLoading">Confirm transfer</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { message } from "@/utils/message";
import type { Company } from "@/api/global_target_companies";
import { getTerritoryList, getCompanyList, postCompany, patchCompany, batchUpdateCompanyUser, batchDeleteCompany,exportExcelCompany } from "@/api/global_target_companies";
import { Delete, Edit, Switch, Download } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import { getUserList } from "@/api/user";

defineOptions({
  name: "Company"
});
const cascaderProps = { multiple: true };
// 状态管理
const companyList = ref([]);
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
});
const searchParams = ref({
  name: "",
  responsible_users: [],
  business_statuses: [],
  territory_full_path: []
});
const formRef = ref();
const isEditMode = ref<boolean>(false);
const dialogVisible = ref<boolean>(false);
const businessStatusOptions = [
  {
    value: 4,
    text: "Not Active"
  },
  {
    value: 3,
    text: "Low Potential"
  },
  {
    value: 2,
    text: "High Potential"
  },
  {
    value: 1,
    text: "Existing"
  }
];
const territoryOptions = ref([]);

const companyForm = ref<Company>({
  id: "",
  user_id: 0,
  name: "",
  address: "",
  contact: "",
  remark: "",
  business_status: 0,
  territory_full_path: []
});
// 验证规则
const rules = ref({
  name: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
  business_status: [{ required: true, message: "请选择business_status", trigger: "blur" }],
  territory_full_path: [{ type: "array", min: 2, required: true, message: "请选择到territory", trigger: "blur" }]
});
// 初始化加载数据
const loadData = async () => {
  let params = {
    name: searchParams.value.name,
    responsible_user_ids: searchParams.value.responsible_users.join(","),
    business_status__in: searchParams.value.business_statuses.join(","),
    territory_ids: searchParams.value.territory_full_path.map(item => item.at(-1)).join(",")
  };
  const res = await getCompanyList({
    ...params,
    page: pagination.value.currentPage,
    size: pagination.value.pageSize
  });

  if (res.success) {
    companyList.value = res.data;
    pagination.value.total = res.total;
  }
};

// 导出Excel处理
const handleExport = async () => {
  exportLoading.value = true;

  try {
    // 复用loadData中的参数构建逻辑
    const params = {
      name: searchParams.value.name,
      responsible_user_ids: searchParams.value.responsible_users.join(","),
      business_status__in: searchParams.value.business_statuses.join(","),
      territory_ids: searchParams.value.territory_full_path.map(item => item.at(-1)).join(",")
    };

    // 调用导出接口
    await exportExcelCompany(params);

    message("导出成功", { type: "success" });
  } catch (error) {
    console.error("导出失败:", error);
    message("导出失败", { type: "error" });
  } finally {
    exportLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.value.currentPage = 1;
  loadData();
};

// 分页处理
const handleSizeChange = val => {
  pagination.value.pageSize = val;
  loadData();
};

const handleCurrentChange = val => {
  pagination.value.currentPage = val;
  loadData();
};

// 对话框关闭处理
const handleDialogClose = () => {
  resetForm();
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 对话框提交处理
async function handleSubmit() {
  // 触发表单验证
  formRef.value?.validate(async valid => {
    if (valid) {
      const id = isEditMode.value ? companyForm.value.id : null;
      const updateData = {
        name: companyForm.value.name,
        address: companyForm.value.address,
        contact: companyForm.value.contact,
        business_status_id: companyForm.value.business_status,
        territory_id: companyForm.value.territory_full_path.at(-1),
        remark: companyForm.value.remark
      };
      try {
        if (isEditMode.value) {
          await patchCompany(id, updateData);
        } else {
          await postCompany(updateData);
        }
        handleDialogClose();
        loadData();
        message(isEditMode.value ? "编辑成功" : "新增成功", { type: "success" });
      } catch (error) {
        message(isEditMode.value ? "编辑失败" : "新增失败", { type: "error" });
      }
    } else {
      return false;
    }
  });
}

function resetForm() {
  // 重置表单
  Object.assign(companyForm.value, {
    id: "",
    name: "",
    address: "",
    contact: "",
    business_status: "",
    territory_id: "",
    remark: "",
    territory_full_path: []
  });
}

function handleAdd() {
  resetForm();
  isEditMode.value = false;
  dialogVisible.value = true;
}

function handleEdit(row) {
  companyForm.value.id = row.id;
  companyForm.value.name = row.name;
  companyForm.value.address = row.address;
  companyForm.value.contact = row.contact;
  companyForm.value.business_status = row.business_status;
  companyForm.value.remark = row.territory.remark;
  companyForm.value.territory_full_path = [row.territory.parent_id, row.territory.id];
  isEditMode.value = true;
  dialogVisible.value = true;
}

// 删除处理
async function batchDelete() {
  if (selectedCompanies.value.length === 0) {
    message("Please select the company you want to delete", { type: "warning" });
    return;
  }
  try {
    const company_ids = selectedCompanies.value.map(company => company.id);
    await ElMessageBox.confirm(`确认删除选择的【${company_ids.length}】个Company吗？`);
    await batchDeleteCompany({ company_ids: company_ids });
    message("删除成功", { type: "success" });
    loadData();
  } catch (error) {
    if (error !== "cancel") {
      message("删除失败", { type: "error" });
    }
  }
}

// 业务状态与CSS类名的映射配置
const statusClassMap = {
  Existing: "row-existing",
  "High Potential": "row-high-potential",
  "Low Potential": "row-low-potential",
  "Not Active": "row-not-active"
};

const tableRowClassName = ({ row }: { row: Company; rowIndex: number }) => {
  return statusClassMap[row.business_status_display] || "";
};

function convertToTreeNode(data) {
  return data.map(item => {
    const node = {
      value: item.id,
      label: item.name
    };

    if (item.children && item.children.length > 0) {
      node.children = convertToTreeNode(item.children);
    }
    return node;
  });
}

async function getTerritoryOptions() {
  const res = await getTerritoryList({ level: 1, recursive: true });
  territoryOptions.value = convertToTreeNode(res.data);
}

async function getBDUsers() {
  // 第一次打开时加载BD用户列表
  if (bdUsers.value.length === 0) {
    try {
      const res = await getUserList({ role_name: "BD" });
      if (res.success) {
        bdUsers.value = res.data;
      }
    } catch (error) {
      message("Failed to load BD list", { type: "error" });
    }
  }
}

// 添加新的状态
const selectedCompanies = ref([]);
const transferDialogVisible = ref(false);
const selectedUserId = ref("");
const bdUsers = ref([]);
const transferLoading = ref(false);
const exportLoading = ref(false);

// 处理表格多选
const handleSelectionChange = selection => {
  selectedCompanies.value = selection;
};

// 处理转移操作
const handleTransfer = async () => {
  // 如果是单个转移，更新 selectedCompanies
  // if (Array.isArray(companies) && companies.length === 1) {
  //   selectedCompanies.value = companies;
  // }
  // 如果是批量转移，selectedCompanies 已经有值了
  if (selectedCompanies.value.length === 0) {
    message("Please select the company you want to transfer", { type: "warning" });
    return;
  }

  transferDialogVisible.value = true;
};

// 确认转移
const handleTransferConfirm = async () => {
  if (!selectedUserId.value) {
    message("Please select a new person in charge", { type: "warning" });
    return;
  }

  transferLoading.value = true;

  try {
    // 构造批量请求数据：公司 ID 列表 + 目标负责人 ID
    const requestData = {
      company_ids: selectedCompanies.value.map(company => company.id),
      user_id: selectedUserId.value
    };

    // 一次请求完成批量更新
    await batchUpdateCompanyUser(requestData);

    // 操作成功后刷新数据

    message(`Transfer Success`, { type: "success" });
    transferDialogVisible.value = false;
    selectedUserId.value = "";
    selectedCompanies.value = [];
    loadData(); // 重新加载数据
  } catch (error) {
    message("Transfer Fail", { type: "error" });
  } finally {
    transferLoading.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  if (history.state?.businessStatus) {
    searchParams.value.business_statuses.push(history.state.businessStatus);
  }
  if (history.state?.responsibleUserId) {
    searchParams.value.responsible_users.push(history.state.responsibleUserId);
  }
  if (history.state?.regionId && history.state?.territoryId) {
    searchParams.value.territory_full_path.push([history.state.regionId, history.state.territoryId]);
  }

  getTerritoryOptions().then(() => {
    const territoryIdFromBD = history.state?.territoryIdFromBD;
    if (territoryIdFromBD) {
      for (let i = 0; i < territoryOptions.value.length; i++) {
        let item = territoryOptions.value[i];
        if (item.value === territoryIdFromBD) {
          item.children.forEach(childItem => {
            searchParams.value.territory_full_path.push([territoryIdFromBD, childItem.value]);
          });
          break;
        }
      }
    }
    loadData();
  });
  getBDUsers();
});
</script>
<style scoped>
.search-bar {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style>
<style>
/* 业务状态行样式 - 使用浅色背景 */
.el-table .row-existing {
  --el-table-tr-bg-color: #f6ffed; /* 浅绿色背景 - 对应绿色 */
}

.el-table .row-high-potential {
  --el-table-tr-bg-color: #fffbe6; /* 浅黄色背景 - 对应黄色 */
}

.el-table .row-low-potential {
  --el-table-tr-bg-color: #fff2f0; /* 浅红色背景 - 对应红色 */
}

.el-table .row-not-active {
  --el-table-tr-bg-color: #f5f5f5; /* 浅灰色背景 - 对应灰色 */
}
</style>
