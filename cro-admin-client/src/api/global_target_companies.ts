import { http } from "@/utils/http";

export type Territory = {
  id: string;
  name: string;
  level: number;
  parent_name?: string;
};

export type TerritoryListResult = {
  success: boolean;
  data: Territory[];
  total: number;
  page: number;
  limit: number;
};

// 城市列表
export const getTerritoryList = (params?: object) => {
  return http.request<TerritoryListResult>("get", "/api/global_target_companies/territory/", { params });
};

// 单个城市详情
export const getTerritoryDetail = (id: string) => {
  return http.request<Territory>("get", `/api/global_target_companies/territory/${id}/`);
};

// 新增城市
export const postTerritory = (data: object) => {
  return http.request("post", "/api/global_target_companies/territory/", { data });
};

// 修改城市
export const patchTerritory = (id: string, data: object) => {
  return http.request("patch", `/api/global_target_companies/territory/${id}/`, { data });
};

// 删除城市
export const deleteTerritory = (id: string) => {
  return http.request("delete", `/api/global_target_companies/territory/${id}/`);
};

export type Company = {
  id: string;
  name: string;
  address?: string;
  contact?: string;
  user_id: number;
  business_status: number;
  remark: string;
  territory_full_path,
};

export type CompanyListResult = {
  success: boolean;
  data: Company[];
  total: number;
  page: number;
  limit: number;
};
export type CompanyHistoryResult = {
  success: boolean;
  data: [];
  total: number;
  page: number;
  limit: number;
};
// 公司列表
export const getCompanyList = (params?: object) => {
  return http.request<CompanyListResult>("get", "/api/global_target_companies/company/", { params });
};

// 公司历史状态查询
export const getCompanyHistory = (params?: object) => {
  return http.request<CompanyHistoryResult>("get", "/api/global_target_companies/company/history/", { params });
};

export const getCompanyStatisticsByTerritory = (params?: object) => {
  return http.request<CompanyHistoryResult>("get", "/api/global_target_companies/company/statistics_by_territory/", { params });
};

export const getCompanyStatisticsByBD = (params?: object) => {
  return http.request<CompanyHistoryResult>("get", "/api/global_target_companies/company/statistics_by_bd/", { params });
};

// 单个公司详情
export const getCompanyDetail = (id: string) => {
  return http.request<Company>("get", `/api/global_target_companies/company/${id}/`);
};

// 新增公司
export const postCompany = (data: object) => {
  return http.request("post", "/api/global_target_companies/company/", { data });
};

// 修改公司
export const patchCompany = (id: string, data: object) => {
  return http.request("patch", `/api/global_target_companies/company/${id}/`, { data });
};

// 公司导出
export const exportExcelCompany = (data: object) => {
  
  return http.request("get", `/api/global_target_companies/company/export_excel/`, { data });
};

// 批量更新公司的负责人
export const batchUpdateCompanyUser = (data: object) => {
  return http.request("patch", `/api/global_target_companies/company/batch_update_user/`, { data });
};

// 删除公司
export const deleteCompany = (id: string) => {
  return http.request("delete", `/api/global_target_companies/company/${id}/`);
};

// 批量删除公司
export const batchDeleteCompany = (data: object) => {
  return http.request("delete", `/api/global_target_companies/company/bulk_delete/`,{data});
};

// 搜索公司
export const searchCompany = (params?: object) => {
  return http.request<CompanyListResult>("get", "/api/global_target_companies/company/search/", { params });
};

// // 导出公司Excel
// export const exportExcelCompany = (params?: object) => {
//   return http.request("get", "/api/global_target_companies/company/export_excel/", {
//     params,
//     responseType: "blob" // 设置响应类型为blob以处理文件下载
//   });
// };
